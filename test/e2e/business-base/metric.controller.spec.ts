import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { getAuthCredentials } from 'test/helpers/authenticated-user';
import { GroupByDate, PortfolioItemStatus } from '@common/enums';

describe('Business Base Metrics controller (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    app = global.__NEST_APP__;
    const { accessToken } = await getAuthCredentials(app, '<EMAIL>', 'password123');
    authToken = accessToken;
  });

  describe('GET /v1/business-base/metrics/portfolio/created', () => {
    it('should return portfolio created metrics for the given date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/created')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/created')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .expect(401);
    });
  });

  describe('GET /v1/business-base/metrics/portfolio/items/created', () => {
    it('should return portfolio items created metrics for the given date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/created')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/created')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .expect(401);
    });
  });

  describe('GET /v1/business-base/metrics/portfolio/items/with-interaction', () => {
    it('should return portfolio items with interaction count for the given date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/with-interaction')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupByDate: GroupByDate.DAY,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/with-interaction')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupByDate: GroupByDate.DAY,
        })
        .expect(401);
    });
  });

  describe('GET /v1/business-base/metrics/portfolio/items/ai-only-interaction', () => {
    it('should return portfolio items with only AI interaction count for the given date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/ai-only-interaction')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/ai-only-interaction')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .expect(401);
    });
  });

  describe('GET /v1/business-base/metrics/portfolio/items/grouped-by-date', () => {
    it('should return portfolio items count by date filtered by status', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/grouped-by-date')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupByDate: GroupByDate.DAY,
          currentStatus: PortfolioItemStatus.IN_PROGRESS,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 when no auth token is provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/items/grouped-by-date')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupByDate: GroupByDate.DAY,
          currentStatus: PortfolioItemStatus.IN_PROGRESS,
        })
        .expect(401);
    });
  });

  describe('GET /v1/business-base/metrics/portfolio/recovered-value', () => {
    it('should return total recovered value metrics without date filtering', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalRecoveredValue).toBeDefined();
      expect(typeof response.body.data.totalRecoveredValue).toBe('number');
      expect(response.body.data.startDate).toBeUndefined();
      expect(response.body.data.endDate).toBeUndefined();
    });

    it('should return total recovered value metrics with date filtering', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalRecoveredValue).toBeDefined();
      expect(typeof response.body.data.totalRecoveredValue).toBe('number');
      expect(response.body.data.startDate).toBe(startDate.toISOString());
      expect(response.body.data.endDate).toBe(endDate.toISOString());
    });

    it('should return total recovered value metrics with only start date', async () => {
      const startDate = new Date('2024-01-01');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
        .query({
          startDate: startDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalRecoveredValue).toBeDefined();
      expect(typeof response.body.data.totalRecoveredValue).toBe('number');
      expect(response.body.data.startDate).toBe(startDate.toISOString());
      expect(response.body.data.endDate).toBeUndefined();
    });

    it('should return total recovered value metrics with only end date', async () => {
      const endDate = new Date('2024-12-31');

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
        .query({
          endDate: endDate.toISOString(),
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalRecoveredValue).toBeDefined();
      expect(typeof response.body.data.totalRecoveredValue).toBe('number');
      expect(response.body.data.startDate).toBeUndefined();
      expect(response.body.data.endDate).toBe(endDate.toISOString());
    });

    it('should return 401 when no auth token is provided', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
        .expect(401);
    });

    it('should handle zero recovered value correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.totalRecoveredValue).toBeDefined();
      expect(typeof response.body.data.totalRecoveredValue).toBe('number');
      expect(response.body.data.totalRecoveredValue).toBeGreaterThanOrEqual(0);
    });
  });
});
