diff --git a/src/business-base/application/controllers/metric.controller.ts b/src/business-base/application/controllers/metric.controller.ts
index 5146194d..812a5305 100644
--- a/src/business-base/application/controllers/metric.controller.ts
+++ b/src/business-base/application/controllers/metric.controller.ts
@@ -2,14 +2,22 @@ import { MetricUseCase } from '@business-base/application/use-cases/metric.use-c
 import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
 import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';
 import { AccountRole, GroupByDate, PortfolioItemStatus, UserRoleInAccount } from '@common/enums';
-import { Controller, Get, Query, Req, Version } from '@nestjs/common';
-import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';
+import { Controller, Get, Inject, Param, Query, Req, Version } from '@nestjs/common';
+import { Api<PERSON>earerAuth, ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';
+import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
+import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
+import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
 
 @UserRolesInAccount(UserRoleInAccount.ADMIN)
 @AccountRoles(AccountRole.BASIC)
 @Controller('business-base/metrics')
 export class MetricsController {
-  constructor(private readonly metricUseCase: MetricUseCase) {}
+  constructor(
+    private readonly metricUseCase: MetricUseCase,
+    private readonly portfolioUseCase: PortfolioUseCase,
+    @Inject('PortfolioItemPort') private readonly portfolioItemAdapter: PortfolioItemPort,
+    @Inject('PortfolioPort') private readonly portfolioAdapter: PortfolioPort,
+  ) {}
 
   @Get('portfolio/created')
   @Version('1')
@@ -147,4 +155,210 @@ export class MetricsController {
       data: metrics,
     };
   }
+
+  @ApiBearerAuth()
+  @ApiOperation({ summary: 'Get total recovered value across all portfolios' })
+  @ApiQuery({
+    name: 'startDate',
+    type: String,
+    format: 'date-time',
+    required: false,
+    example: '2024-01-01T00:00:00.000Z',
+    description: 'Start date for recovered value filtering',
+  })
+  @ApiQuery({
+    name: 'endDate',
+    type: String,
+    format: 'date-time',
+    required: false,
+    example: '2024-12-31T23:59:59.999Z',
+    description: 'End date for recovered value filtering',
+  })
+  @ApiResponse({
+    status: 200,
+    description: 'Total recovered value across all portfolios',
+    schema: {
+      example: {
+        statusCode: 200,
+        data: {
+          totalRecoveredValue: 1500000,
+          startDate: '2024-01-01T00:00:00.000Z',
+          endDate: '2024-12-31T23:59:59.999Z',
+        },
+      },
+    },
+  })
+  @Get('portfolio/recovered-value')
+  @Version('1')
+  async getTotalRecoveredValue(
+    @Req() request: Request,
+    @Query('startDate') startDate?: Date,
+    @Query('endDate') endDate?: Date,
+  ) {
+    const { customerId } = request['user'];
+
+    const totalRecoveredValue = await this.portfolioUseCase.getTotalRecoveredValueByCustomer(
+      customerId,
+      startDate,
+      endDate,
+    );
+
+    return {
+      statusCode: 200,
+      data: {
+        totalRecoveredValue: totalRecoveredValue,
+        startDate: startDate ? new Date(startDate).toISOString() : undefined,
+        endDate: endDate ? new Date(endDate).toISOString() : undefined,
+      },
+    };
+  }
+
+  @ApiOperation({ summary: 'Get average ticket for a portfolio' })
+  @ApiQuery({
+    name: 'startDate',
+    type: String,
+    format: 'date-time',
+    required: false,
+    example: '2024-01-01T00:00:00.000Z',
+    description: 'Start date for recovered value filtering',
+  })
+  @ApiQuery({
+    name: 'endDate',
+    type: String,
+    format: 'date-time',
+    required: false,
+    example: '2024-12-31T23:59:59.999Z',
+    description: 'End date for recovered value filtering',
+  })
+  @ApiResponse({
+    status: 200,
+    description: 'Average ticket for a portfolio',
+    schema: {
+      example: {
+        statusCode: 200,
+        data: {
+          averageTicket: 1500000,
+          startDate: '2024-01-01T00:00:00.000Z',
+          endDate: '2024-12-31T23:59:59.999Z',
+        },
+      },
+    },
+  })
+  @ApiResponse({ status: 401, description: 'Unauthorized' })
+  @ApiResponse({ status: 404, description: 'Portfolio not found' })
+  @ApiBearerAuth()
+  @Get('portfolio/average-ticket/:portfolioId')
+  @Version('1')
+  async getAverageTicket(
+    @Req() request: Request,
+    @Param('portfolioId') portfolioId: string,
+    @Query('startDate') startDate?: Date,
+    @Query('endDate') endDate?: Date,
+  ) {
+    const { customerId } = request['user'];
+
+    const totalRecoveredValue = await this.portfolioUseCase.getTotalRecoveredValueByCustomer(
+      customerId,
+      startDate,
+      endDate,
+    );
+
+    const totalPortfolioItems = await this.portfolioItemAdapter.countSuccessfulByPortfolioId(
+      portfolioId,
+    );
+
+    const averageTicket = totalRecoveredValue / totalPortfolioItems;
+
+    const formattedValue = new Intl.NumberFormat('pt-BR', {
+      style: 'currency',
+      currency: 'BRL',
+    }).format(averageTicket / 100);
+
+    return {
+      statusCode: 200,
+      data: {
+        averageTicket: formattedValue,
+        startDate: startDate ? new Date(startDate).toISOString() : undefined,
+        endDate: endDate ? new Date(endDate).toISOString() : undefined,
+      },
+    };
+  }
+
+  @ApiOperation({ summary: 'Get customer average ticket across all portfolios' })
+  @ApiQuery({
+    name: 'startDate',
+    type: String,
+    format: 'date-time',
+    required: false,
+    example: '2024-01-01T00:00:00.000Z',
+    description: 'Start date for recovered value filtering',
+  })
+  @ApiQuery({
+    name: 'endDate',
+    type: String,
+    format: 'date-time',
+    required: false,
+    example: '2024-12-31T23:59:59.999Z',
+    description: 'End date for recovered value filtering',
+  })
+  @ApiResponse({
+    status: 200,
+    description: 'Customer average ticket calculated successfully',
+    schema: {
+      example: {
+        statusCode: 200,
+        data: {
+          averageTicket: 'R$ 1.250,75',
+          startDate: '2024-01-01T00:00:00.000Z',
+          endDate: '2024-12-31T23:59:59.999Z',
+        },
+      },
+    },
+  })
+  @ApiResponse({ status: 401, description: 'Unauthorized' })
+  @ApiBearerAuth()
+  @Get('customer/average-ticket')
+  @Version('1')
+  async getCustomerAverageTicket(
+    @Req() request: Request,
+    @Query('startDate') startDate?: Date,
+    @Query('endDate') endDate?: Date,
+  ) {
+    const { customerId } = request['user'];
+
+    // Get total recovered value for customer across all portfolios
+    const totalRecoveredValue = await this.portfolioUseCase.getTotalRecoveredValueByCustomer(
+      customerId,
+      startDate,
+      endDate,
+    );
+
+    // Get all portfolios for the customer and sum successful items
+    const customerPortfolios = await this.portfolioAdapter.getAll({ customerId });
+    let totalSuccessfulItems = 0;
+
+    for (const portfolio of customerPortfolios) {
+      const portfolioSuccessfulItems = await this.portfolioItemAdapter.countSuccessfulByPortfolioId(
+        portfolio.id,
+      );
+      totalSuccessfulItems += portfolioSuccessfulItems;
+    }
+
+    // Calculate average ticket
+    const averageTicket = totalSuccessfulItems > 0 ? totalRecoveredValue / totalSuccessfulItems : 0;
+
+    const formattedValue = new Intl.NumberFormat('pt-BR', {
+      style: 'currency',
+      currency: 'BRL',
+    }).format(averageTicket / 100);
+
+    return {
+      statusCode: 200,
+      data: {
+        averageTicket: formattedValue,
+        startDate: startDate ? new Date(startDate).toISOString() : undefined,
+        endDate: endDate ? new Date(endDate).toISOString() : undefined,
+      },
+    };
+  }
 }
diff --git a/src/business-base/application/controllers/portfolio.controller.ts b/src/business-base/application/controllers/portfolio.controller.ts
index a42855a5..ce86ff01 100644
--- a/src/business-base/application/controllers/portfolio.controller.ts
+++ b/src/business-base/application/controllers/portfolio.controller.ts
@@ -6,6 +6,7 @@ import {
   Param,
   Post,
   Put,
+  Query,
   Req,
   StreamableFile,
   UploadedFile,
@@ -25,6 +26,7 @@ import {
   ApiBody,
   ApiConsumes,
   ApiOperation,
+  ApiQuery,
   ApiResponse,
   ApiTags,
   ApiBearerAuth,
@@ -321,11 +323,38 @@ export class PortfolioController {
     };
   }
 
+  @ApiBearerAuth()
+  @ApiOperation({ summary: 'Get all portfolios with optional recovered value filtering' })
+  @ApiQuery({
+    name: 'startDate',
+    type: String,
+    format: 'date-time',
+    required: false,
+    example: '2024-01-01T00:00:00.000Z',
+    description: 'Start date for recovered value filtering',
+  })
+  @ApiQuery({
+    name: 'endDate',
+    type: String,
+    format: 'date-time',
+    required: false,
+    example: '2024-12-31T23:59:59.999Z',
+    description: 'End date for recovered value filtering',
+  })
+  @ApiResponse({
+    status: 200,
+    description: 'Successfully retrieved portfolios with recovered value metrics',
+    type: [ResponsePortfolioDto],
+  })
   @Get()
   @Version('1')
-  async findAll(@Req() request: Request): Promise<any> {
+  async findAll(
+    @Req() request: Request,
+    @Query('startDate') startDate?: Date,
+    @Query('endDate') endDate?: Date,
+  ): Promise<any> {
     const { customerId } = request['user'];
-    const portfolios = await this.portfolioUseCase.findAll(customerId);
+    const portfolios = await this.portfolioUseCase.findAll(customerId, startDate, endDate);
 
     return {
       statusCode: 200,
@@ -333,11 +362,48 @@ export class PortfolioController {
     };
   }
 
+  @ApiBearerAuth()
+  @ApiOperation({ summary: 'Get portfolio by ID with optional recovered value filtering' })
+  @ApiQuery({
+    name: 'startDate',
+    type: String,
+    format: 'date-time',
+    required: false,
+    example: '2024-01-01T00:00:00.000Z',
+    description: 'Start date for recovered value filtering',
+  })
+  @ApiQuery({
+    name: 'endDate',
+    type: String,
+    format: 'date-time',
+    required: false,
+    example: '2024-12-31T23:59:59.999Z',
+    description: 'End date for recovered value filtering',
+  })
+  @ApiResponse({
+    status: 200,
+    description: 'Successfully retrieved portfolio with recovered value metrics',
+    type: ResponsePortfolioDto,
+  })
+  @ApiResponse({
+    status: 404,
+    description: 'Portfolio not found',
+  })
   @Get('/:portfolioId')
   @Version('1')
-  async findById(@Param('portfolioId') portfolioId: string, @Req() request: Request): Promise<any> {
+  async findById(
+    @Param('portfolioId') portfolioId: string,
+    @Req() request: Request,
+    @Query('startDate') startDate?: Date,
+    @Query('endDate') endDate?: Date,
+  ): Promise<any> {
     const customerId = request['user'].customerId;
-    const portfolio = await this.portfolioUseCase.findById(portfolioId, customerId);
+    const portfolio = await this.portfolioUseCase.findById(
+      portfolioId,
+      customerId,
+      startDate,
+      endDate,
+    );
 
     return {
       statusCode: 200,
@@ -345,6 +411,11 @@ export class PortfolioController {
     };
   }
 
+  @ApiOperation({ summary: 'Get all portfolio performance' })
+  @ApiResponse({
+    status: 200,
+    description: 'Successfully retrieved all portfolio performance',
+  })
   @Get('/performance/all')
   @Version('1')
   async findAllPortfolioPerformance(@Req() request: Request): Promise<any> {
@@ -357,6 +428,15 @@ export class PortfolioController {
     };
   }
 
+  @ApiOperation({ summary: 'Get portfolio performance by ID' })
+  @ApiResponse({
+    status: 200,
+    description: 'Successfully retrieved portfolio performance by ID',
+  })
+  @ApiResponse({
+    status: 404,
+    description: 'Portfolio not found',
+  })
   @Get('/:portfolioId/performance')
   @Version('1')
   async findPortfolioPerformanceById(
@@ -375,6 +455,7 @@ export class PortfolioController {
     };
   }
 
+  @ApiBearerAuth()
   @Get('/:portfolioId/download')
   @Version('1')
   async downloadPortfolioOriginalFile(
diff --git a/src/business-base/application/dto/out/response-portfolio.dto.ts b/src/business-base/application/dto/out/response-portfolio.dto.ts
index 21f2ed11..b717a2a1 100644
--- a/src/business-base/application/dto/out/response-portfolio.dto.ts
+++ b/src/business-base/application/dto/out/response-portfolio.dto.ts
@@ -1,6 +1,7 @@
-import { IsBoolean, IsDate, IsEnum, IsNumber, IsString, IsUUID } from 'class-validator';
+import { IsBoolean, IsDate, IsEnum, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
 import { PortfolioExecutionStatus, PortfolioImportStatus } from '@common/enums';
 import { PortfolioDto } from '@business-base/application/dto/in/portfolio.dto';
+import { ApiProperty } from '@nestjs/swagger';
 
 export class ResponsePortfolioDto extends PortfolioDto {
   @IsUUID('4')
@@ -59,4 +60,13 @@ export class ResponsePortfolioDto extends PortfolioDto {
 
   @IsString()
   readonly timezoneUTC: string = '-3';
+
+  @ApiProperty({
+    description: 'Total recovered value formatted as BRL currency',
+    example: 'R$ 1.500,00',
+    required: false,
+  })
+  @IsString()
+  @IsOptional()
+  readonly recoveredValue?: string;
 }
diff --git a/src/business-base/application/use-cases/portfolio.use-case.ts b/src/business-base/application/use-cases/portfolio.use-case.ts
index 52075b3f..b36426f4 100644
--- a/src/business-base/application/use-cases/portfolio.use-case.ts
+++ b/src/business-base/application/use-cases/portfolio.use-case.ts
@@ -49,6 +49,7 @@ import {
   CustomImportConfigDto,
 } from '@business-base/application/dto/customer-preferences.dto';
 import { CorrelationContextService } from '@common/services/correlation-context.service';
+import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
 
 @Injectable()
 export class PortfolioUseCase {
@@ -75,6 +76,8 @@ export class PortfolioUseCase {
     @Inject('InfraWorkflowPort')
     private readonly workflowAdapter: InfraWorkflowPort,
     private readonly customerPreferencesUseCase: CustomerPreferencesUseCase,
+    @Inject('CollectCashStatsPort')
+    private readonly collectCashStatsAdapter: CollectCashStatsPort,
   ) {
     this.portfolioImportFilesBucketName = process.env.PORTFOLIO_IMPORT_FILES_BUCKET;
   }
@@ -234,15 +237,35 @@ export class PortfolioUseCase {
     }
   }
 
-  async findAll(customerId: string): Promise<ResponsePortfolioDto[]> {
+  async findAll(
+    customerId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<ResponsePortfolioDto[]> {
     logger.debug('Finding all portfolios');
     let portfolios = await this.portfolioAdapter.getAll({ customerId });
     //order by createdAt desc
     portfolios = portfolios.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
 
-    return portfolios.map(portfolio => {
-      return this.createResponsePortfolioDto(portfolio);
-    });
+    const portfoliosWithRecoveredValue = await Promise.all(
+      portfolios.map(async portfolio => {
+        const recoveredValue = await this.getPortfolioRecoveredValue(
+          portfolio.id,
+          startDate,
+          endDate,
+        );
+        const formattedValue = new Intl.NumberFormat('pt-BR', {
+          style: 'currency',
+          currency: 'BRL',
+        }).format(recoveredValue / 100);
+        return {
+          ...this.createResponsePortfolioDto(portfolio),
+          recoveredValue: formattedValue,
+        };
+      }),
+    );
+
+    return portfoliosWithRecoveredValue;
   }
 
   async findAllImporting(): Promise<ResponsePortfolioDto[]> {
@@ -388,10 +411,23 @@ export class PortfolioUseCase {
     return portfoliosIds;
   }
 
-  async findById(portfolioId: string, customerId: string): Promise<ResponsePortfolioDto> {
+  async findById(
+    portfolioId: string,
+    customerId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<ResponsePortfolioDto> {
     const portfolio = await this.getPortfolioEntity(portfolioId, customerId);
+    const recoveredValue = await this.getPortfolioRecoveredValue(portfolioId, startDate, endDate);
+    const formattedValue = new Intl.NumberFormat('pt-BR', {
+      style: 'currency',
+      currency: 'BRL',
+    }).format(recoveredValue / 100);
 
-    return this.createResponsePortfolioDto(portfolio);
+    return {
+      ...this.createResponsePortfolioDto(portfolio),
+      recoveredValue: formattedValue,
+    };
   }
 
   async findByIdInternal(portfolioId: string): Promise<ResponsePortfolioDto> {
@@ -2069,4 +2105,40 @@ export class PortfolioUseCase {
       return mappedData as T;
     }
   }
+
+  /**
+   * Get recovered value for a specific portfolio with optional date filtering
+   */
+  private async getPortfolioRecoveredValue(
+    portfolioId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<number> {
+    if (startDate || endDate) {
+      return await this.collectCashStatsAdapter.getTotalRecoveredValueByPortfolioIdWithDateRange(
+        portfolioId,
+        startDate,
+        endDate,
+      );
+    }
+    return await this.collectCashStatsAdapter.getTotalRecoveredValueByPortfolioId(portfolioId);
+  }
+
+  /**
+   * Get total recovered value for a customer across all portfolios with optional date filtering
+   */
+  async getTotalRecoveredValueByCustomer(
+    customerId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<number> {
+    if (startDate || endDate) {
+      return await this.collectCashStatsAdapter.getTotalRecoveredValueByCustomerIdWithDateRange(
+        customerId,
+        startDate,
+        endDate,
+      );
+    }
+    return await this.collectCashStatsAdapter.getTotalRecoveredValueByCustomerId(customerId);
+  }
 }
diff --git a/src/business-base/application/use-cases/statistical-data.use-case.ts b/src/business-base/application/use-cases/statistical-data.use-case.ts
index b5bd51c3..e5e18895 100644
--- a/src/business-base/application/use-cases/statistical-data.use-case.ts
+++ b/src/business-base/application/use-cases/statistical-data.use-case.ts
@@ -42,7 +42,7 @@ export class StatisticalDataUseCase {
     private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort,
     @Inject('InfraWorkflowPort')
     private readonly workflowAdapter: InfraWorkflowPort,
-  ) { }
+  ) {}
 
   /**
    * Extracts and stores statistical data for a portfolio item based on workflow configuration
diff --git a/src/business-base/infrastructure/adapters/db/collect-cash-stats.adapter.ts b/src/business-base/infrastructure/adapters/db/collect-cash-stats.adapter.ts
index 49d5a31c..5681b46d 100644
--- a/src/business-base/infrastructure/adapters/db/collect-cash-stats.adapter.ts
+++ b/src/business-base/infrastructure/adapters/db/collect-cash-stats.adapter.ts
@@ -28,39 +28,39 @@ export class CollectCashStatsAdapter
     return stats.map(stat => this.mapToEntity(stat));
   }
 
-  async findByPortfolioId(portfolioId: string): Promise<CollectCashStatsEntity[]> {
-    const stats = await this.prisma.client.collectCashStats.findMany({
+  async getTotalRecoveredValueByCustomerId(customerId: string): Promise<number> {
+    const result = await this.prisma.client.collectCashStats.aggregate({
       where: {
-        portfolioId,
+        customerId,
         status: RecordStatus.ACTIVE,
       },
-      orderBy: {
-        createdAt: 'desc',
+      _sum: {
+        dealValue: true,
       },
     });
 
-    return stats.map(stat => this.mapToEntity(stat));
+    return Number(result._sum.dealValue) || 0;
   }
 
-  async findByPortfolioItemId(portfolioItemId: string): Promise<CollectCashStatsEntity[]> {
-    const stats = await this.prisma.client.collectCashStats.findMany({
+  async getTotalRecoveredValueByPortfolioId(portfolioId: string): Promise<number> {
+    const result = await this.prisma.client.collectCashStats.aggregate({
       where: {
-        portfolioItemId,
+        portfolioId,
         status: RecordStatus.ACTIVE,
       },
-      orderBy: {
-        createdAt: 'desc',
+      _sum: {
+        dealValue: true,
       },
     });
 
-    return stats.map(stat => this.mapToEntity(stat));
+    return Number(result._sum.dealValue) || 0;
   }
 
-  async findByCustomerIdWithDateRange(
+  async getTotalRecoveredValueByCustomerIdWithDateRange(
     customerId: string,
     startDate?: Date,
     endDate?: Date,
-  ): Promise<CollectCashStatsEntity[]> {
+  ): Promise<number> {
     const whereClause: any = {
       customerId,
       status: RecordStatus.ACTIVE,
@@ -76,66 +76,8 @@ export class CollectCashStatsAdapter
       }
     }
 
-    const stats = await this.prisma.client.collectCashStats.findMany({
-      where: whereClause,
-      orderBy: {
-        createdAt: 'desc',
-      },
-    });
-
-    return stats.map(stat => this.mapToEntity(stat));
-  }
-
-  async findByPortfolioIdWithDateRange(
-    portfolioId: string,
-    startDate?: Date,
-    endDate?: Date,
-  ): Promise<CollectCashStatsEntity[]> {
-    const whereClause: any = {
-      portfolioId,
-      status: RecordStatus.ACTIVE,
-    };
-
-    if (startDate || endDate) {
-      whereClause.createdAt = {};
-      if (startDate) {
-        whereClause.createdAt.gte = startDate;
-      }
-      if (endDate) {
-        whereClause.createdAt.lte = endDate;
-      }
-    }
-
-    const stats = await this.prisma.client.collectCashStats.findMany({
-      where: whereClause,
-      orderBy: {
-        createdAt: 'desc',
-      },
-    });
-
-    return stats.map(stat => this.mapToEntity(stat));
-  }
-
-  async getTotalRecoveredValueByCustomerId(customerId: string): Promise<number> {
     const result = await this.prisma.client.collectCashStats.aggregate({
-      where: {
-        customerId,
-        status: RecordStatus.ACTIVE,
-      },
-      _sum: {
-        dealValue: true,
-      },
-    });
-
-    return Number(result._sum.dealValue) || 0;
-  }
-
-  async getTotalRecoveredValueByPortfolioId(portfolioId: string): Promise<number> {
-    const result = await this.prisma.client.collectCashStats.aggregate({
-      where: {
-        portfolioId,
-        status: RecordStatus.ACTIVE,
-      },
+      where: whereClause,
       _sum: {
         dealValue: true,
       },
@@ -144,13 +86,13 @@ export class CollectCashStatsAdapter
     return Number(result._sum.dealValue) || 0;
   }
 
-  async getTotalRecoveredValueByCustomerIdWithDateRange(
-    customerId: string,
+  async getTotalRecoveredValueByPortfolioIdWithDateRange(
+    portfolioId: string,
     startDate?: Date,
     endDate?: Date,
   ): Promise<number> {
     const whereClause: any = {
-      customerId,
+      portfolioId,
       status: RecordStatus.ACTIVE,
     };
 
diff --git a/src/business-base/infrastructure/adapters/db/portfolio-item.adapter.ts b/src/business-base/infrastructure/adapters/db/portfolio-item.adapter.ts
index c35b8476..6e4a1898 100644
--- a/src/business-base/infrastructure/adapters/db/portfolio-item.adapter.ts
+++ b/src/business-base/infrastructure/adapters/db/portfolio-item.adapter.ts
@@ -328,6 +328,15 @@ export class PortfolioItemAdapter
     });
   }
 
+  async countSuccessfulByPortfolioId(portfolioId: string): Promise<number> {
+    return this.prisma.client.portfolioItem.count({
+      where: {
+        currentStatus: PortfolioItemStatus.SUCCEED,
+        portfolioId,
+      },
+    });
+  }
+
   async updateBatchToIdle(date: Date, batchSize: number): Promise<number> {
     const query = Prisma.sql`
       WITH to_update AS (SELECT pi.id
diff --git a/src/business-base/infrastructure/ports/db/collect-cash-stats.port.ts b/src/business-base/infrastructure/ports/db/collect-cash-stats.port.ts
index ae4c0dcc..7839ba44 100644
--- a/src/business-base/infrastructure/ports/db/collect-cash-stats.port.ts
+++ b/src/business-base/infrastructure/ports/db/collect-cash-stats.port.ts
@@ -2,19 +2,6 @@ import { DbCommonPort } from '@common/db/ports/common.port';
 import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
 
 export interface CollectCashStatsPort extends DbCommonPort<CollectCashStatsEntity> {
-  findByCustomerId(customerId: string): Promise<CollectCashStatsEntity[]>;
-  findByPortfolioId(portfolioId: string): Promise<CollectCashStatsEntity[]>;
-  findByPortfolioItemId(portfolioItemId: string): Promise<CollectCashStatsEntity[]>;
-  findByCustomerIdWithDateRange(
-    customerId: string,
-    startDate?: Date,
-    endDate?: Date,
-  ): Promise<CollectCashStatsEntity[]>;
-  findByPortfolioIdWithDateRange(
-    portfolioId: string,
-    startDate?: Date,
-    endDate?: Date,
-  ): Promise<CollectCashStatsEntity[]>;
   getTotalRecoveredValueByCustomerId(customerId: string): Promise<number>;
   getTotalRecoveredValueByPortfolioId(portfolioId: string): Promise<number>;
   getTotalRecoveredValueByCustomerIdWithDateRange(
@@ -22,4 +9,9 @@ export interface CollectCashStatsPort extends DbCommonPort<CollectCashStatsEntit
     startDate?: Date,
     endDate?: Date,
   ): Promise<number>;
+  getTotalRecoveredValueByPortfolioIdWithDateRange(
+    portfolioId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<number>;
 }
diff --git a/src/business-base/infrastructure/ports/db/portfolio-item.port.ts b/src/business-base/infrastructure/ports/db/portfolio-item.port.ts
index 9a0ba6d8..d7007fb3 100644
--- a/src/business-base/infrastructure/ports/db/portfolio-item.port.ts
+++ b/src/business-base/infrastructure/ports/db/portfolio-item.port.ts
@@ -6,6 +6,7 @@ import { PortfolioItemStatus } from '@common/enums';
 
 export interface PortfolioItemPort extends DbCommonPort<PortfolioItemEntity> {
   countByPortfolioId(portfolioId: string): Promise<number>;
+  countSuccessfulByPortfolioId(portfolioId: string): Promise<number>;
 
   fetchAndQueuePendingItems(
     portfolioId: string,
diff --git a/src/orchestrator/application/controllers/workflow.controller.ts b/src/orchestrator/application/controllers/workflow.controller.ts
index aa7de4c6..30a8d041 100644
--- a/src/orchestrator/application/controllers/workflow.controller.ts
+++ b/src/orchestrator/application/controllers/workflow.controller.ts
@@ -21,7 +21,7 @@ import { ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
 @ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
 @Controller('orchestrator/workflows')
 export class WorkflowController {
-  constructor(private readonly workflowUseCase: WorkflowUseCase) { }
+  constructor(private readonly workflowUseCase: WorkflowUseCase) {}
 
   @Get('health')
   @Version('1')
diff --git a/test/e2e/business-base/metric.controller.spec.ts b/test/e2e/business-base/metric.controller.spec.ts
index ********..5c4a11aa 100644
--- a/test/e2e/business-base/metric.controller.spec.ts
+++ b/test/e2e/business-base/metric.controller.spec.ts
@@ -183,4 +183,163 @@ describe('Business Base Metrics controller (e2e)', () => {
         .expect(401);
     });
   });
+
+  describe('GET /v1/business-base/metrics/portfolio/recovered-value', () => {
+    it('should return total recovered value metrics without date filtering', async () => {
+      const response = await request(app.getHttpServer())
+        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
+        .set('Authorization', `Bearer ${authToken}`)
+        .expect(200);
+
+      expect(response.body).toBeDefined();
+      expect(response.body.statusCode).toBe(200);
+      expect(response.body.data).toBeDefined();
+      expect(response.body.data.totalRecoveredValue).toBeDefined();
+      expect(typeof response.body.data.totalRecoveredValue).toBe('number');
+      expect(response.body.data.startDate).toBeUndefined();
+      expect(response.body.data.endDate).toBeUndefined();
+    });
+
+    it('should return total recovered value metrics with date filtering', async () => {
+      const startDate = new Date('2024-01-01');
+      const endDate = new Date('2024-12-31');
+
+      const response = await request(app.getHttpServer())
+        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
+        .query({
+          startDate: startDate.toISOString(),
+          endDate: endDate.toISOString(),
+        })
+        .set('Authorization', `Bearer ${authToken}`)
+        .expect(200);
+
+      expect(response.body).toBeDefined();
+      expect(response.body.statusCode).toBe(200);
+      expect(response.body.data).toBeDefined();
+      expect(response.body.data.totalRecoveredValue).toBeDefined();
+      expect(typeof response.body.data.totalRecoveredValue).toBe('number');
+      expect(response.body.data.startDate).toBe(startDate.toISOString());
+      expect(response.body.data.endDate).toBe(endDate.toISOString());
+    });
+
+    it('should return total recovered value metrics with only start date', async () => {
+      const startDate = new Date('2024-01-01');
+
+      const response = await request(app.getHttpServer())
+        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
+        .query({
+          startDate: startDate.toISOString(),
+        })
+        .set('Authorization', `Bearer ${authToken}`)
+        .expect(200);
+
+      expect(response.body).toBeDefined();
+      expect(response.body.statusCode).toBe(200);
+      expect(response.body.data).toBeDefined();
+      expect(response.body.data.totalRecoveredValue).toBeDefined();
+      expect(typeof response.body.data.totalRecoveredValue).toBe('number');
+      expect(response.body.data.startDate).toBe(startDate.toISOString());
+      expect(response.body.data.endDate).toBeUndefined();
+    });
+
+    it('should return total recovered value metrics with only end date', async () => {
+      const endDate = new Date('2024-12-31');
+
+      const response = await request(app.getHttpServer())
+        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
+        .query({
+          endDate: endDate.toISOString(),
+        })
+        .set('Authorization', `Bearer ${authToken}`)
+        .expect(200);
+
+      expect(response.body).toBeDefined();
+      expect(response.body.statusCode).toBe(200);
+      expect(response.body.data).toBeDefined();
+      expect(response.body.data.totalRecoveredValue).toBeDefined();
+      expect(typeof response.body.data.totalRecoveredValue).toBe('number');
+      expect(response.body.data.startDate).toBeUndefined();
+      expect(response.body.data.endDate).toBe(endDate.toISOString());
+    });
+
+    it('should return 401 when no auth token is provided', async () => {
+      await request(app.getHttpServer())
+        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
+        .expect(401);
+    });
+
+    it('should handle zero recovered value correctly', async () => {
+      const response = await request(app.getHttpServer())
+        .get('/api/v1/business-base/metrics/portfolio/recovered-value')
+        .set('Authorization', `Bearer ${authToken}`)
+        .expect(200);
+
+      expect(response.body).toBeDefined();
+      expect(response.body.statusCode).toBe(200);
+      expect(response.body.data).toBeDefined();
+      expect(response.body.data.totalRecoveredValue).toBeDefined();
+      expect(typeof response.body.data.totalRecoveredValue).toBe('number');
+      expect(response.body.data.totalRecoveredValue).toBeGreaterThanOrEqual(0);
+    });
+  });
+
+  describe('GET /v1/business-base/metrics/customer/average-ticket', () => {
+    it('should return customer average ticket across all portfolios without date filtering', async () => {
+      const response = await request(app.getHttpServer())
+        .get('/api/v1/business-base/metrics/customer/average-ticket')
+        .set('Authorization', `Bearer ${authToken}`)
+        .expect(200);
+
+      expect(response.body).toBeDefined();
+      expect(response.body.statusCode).toBe(200);
+      expect(response.body.data).toBeDefined();
+      expect(response.body.data.averageTicket).toBeDefined();
+      expect(typeof response.body.data.averageTicket).toBe('string');
+      expect(response.body.data.startDate).toBeUndefined();
+      expect(response.body.data.endDate).toBeUndefined();
+    });
+
+    it('should return customer average ticket with date filtering', async () => {
+      const startDate = new Date('2024-01-01');
+      const endDate = new Date('2024-12-31');
+
+      const response = await request(app.getHttpServer())
+        .get('/api/v1/business-base/metrics/customer/average-ticket')
+        .query({
+          startDate: startDate.toISOString(),
+          endDate: endDate.toISOString(),
+        })
+        .set('Authorization', `Bearer ${authToken}`)
+        .expect(200);
+
+      expect(response.body).toBeDefined();
+      expect(response.body.statusCode).toBe(200);
+      expect(response.body.data).toBeDefined();
+      expect(response.body.data.averageTicket).toBeDefined();
+      expect(typeof response.body.data.averageTicket).toBe('string');
+      expect(response.body.data.startDate).toBe(startDate.toISOString());
+      expect(response.body.data.endDate).toBe(endDate.toISOString());
+    });
+
+    it('should return 401 when no auth token is provided', async () => {
+      await request(app.getHttpServer())
+        .get('/api/v1/business-base/metrics/customer/average-ticket')
+        .expect(401);
+    });
+
+    it('should handle zero successful items correctly', async () => {
+      const response = await request(app.getHttpServer())
+        .get('/api/v1/business-base/metrics/customer/average-ticket')
+        .set('Authorization', `Bearer ${authToken}`)
+        .expect(200);
+
+      expect(response.body).toBeDefined();
+      expect(response.body.statusCode).toBe(200);
+      expect(response.body.data).toBeDefined();
+      expect(response.body.data.averageTicket).toBeDefined();
+      expect(typeof response.body.data.averageTicket).toBe('string');
+      // Should be formatted as currency even if 0
+      expect(response.body.data.averageTicket).toMatch(/R\$\s[\d.,]+/);
+    });
+  });
 });
diff --git a/test/e2e/business-base/portfolio.controller.spec.ts b/test/e2e/business-base/portfolio.controller.spec.ts
index 28249b5d..6aa4fb30 100644
--- a/test/e2e/business-base/portfolio.controller.spec.ts
+++ b/test/e2e/business-base/portfolio.controller.spec.ts
@@ -984,4 +984,168 @@ describe('Portfolio (e2e)', () => {
     expect(response.status).toBe(201);
     return response.body;
   }
+
+  describe('Recovered Value Functionality', () => {
+    it('/v1/business-base/portfolios (GET) - Should include recoveredValue in response', async () => {
+      const portfolio = createPortfolioFake('Portfolio Recovered Value Test', 5, true);
+      const customerFake = await createCustomerFake(
+        'Recovered Value User',
+        'recovered_value_user',
+        '<EMAIL>',
+        '88.714.999/0001-02',
+        '+5511987555999',
+      );
+
+      // Create portfolio
+      const { body: createBody } = await request(app.getHttpServer())
+        .post('/api/v1/business-base/portfolios/import')
+        .attach('file', `${__dirname}/data/test-portfolio.csv`)
+        .field('name', portfolio.name)
+        .field('workflowId', testWorkflowId)
+        .field('workExpression', portfolio.workExpression)
+        .field('executeImmediately', portfolio.executeImmediately.toString())
+        .field('idleAfter', portfolio.idleAfter.toString())
+        .field('communicationChannel', portfolio.communicationChannel)
+        .set('Authorization', `Bearer ${customerFake.token}`)
+        .expect(201);
+
+      // Get all portfolios
+      const { body: getAllBody } = await request(app.getHttpServer())
+        .get('/api/v1/business-base/portfolios')
+        .set('Authorization', `Bearer ${customerFake.token}`)
+        .expect(200);
+
+      expect(getAllBody).toBeDefined();
+      expect(getAllBody.data).toBeDefined();
+      expect(Array.isArray(getAllBody.data)).toBe(true);
+      expect(getAllBody.data.length).toBeGreaterThanOrEqual(1);
+
+      // Check that recoveredValue is included in the response
+      const createdPortfolio = getAllBody.data.find(p => p.id === createBody.data.id);
+      expect(createdPortfolio).toBeDefined();
+      expect(createdPortfolio.recoveredValue).toBeDefined();
+      expect(typeof createdPortfolio.recoveredValue).toBe('string');
+    });
+
+    it('/v1/business-base/portfolios (GET) - Should filter recoveredValue by date range', async () => {
+      const portfolio = createPortfolioFake('Portfolio Date Filter Test', 5, true);
+      const customerFake = await createCustomerFake(
+        'Date Filter User',
+        'date_filter_user',
+        '<EMAIL>',
+        '88.714.998/0001-02',
+        '+5511987555998',
+      );
+
+      // Create portfolio
+      await request(app.getHttpServer())
+        .post('/api/v1/business-base/portfolios/import')
+        .attach('file', `${__dirname}/data/test-portfolio.csv`)
+        .field('name', portfolio.name)
+        .field('workflowId', testWorkflowId)
+        .field('workExpression', portfolio.workExpression)
+        .field('executeImmediately', portfolio.executeImmediately.toString())
+        .field('idleAfter', portfolio.idleAfter.toString())
+        .field('communicationChannel', portfolio.communicationChannel)
+        .set('Authorization', `Bearer ${customerFake.token}`)
+        .expect(201);
+
+      // Get portfolios with date filtering
+      const startDate = '2024-01-01T00:00:00.000Z';
+      const endDate = '2024-12-31T23:59:59.999Z';
+
+      const { body: getFilteredBody } = await request(app.getHttpServer())
+        .get('/api/v1/business-base/portfolios')
+        .query({ startDate, endDate })
+        .set('Authorization', `Bearer ${customerFake.token}`)
+        .expect(200);
+
+      expect(getFilteredBody).toBeDefined();
+      expect(getFilteredBody.data).toBeDefined();
+      expect(Array.isArray(getFilteredBody.data)).toBe(true);
+
+      // All portfolios should have recoveredValue field
+      getFilteredBody.data.forEach(portfolio => {
+        expect(portfolio.recoveredValue).toBeDefined();
+        expect(typeof portfolio.recoveredValue).toBe('string');
+      });
+    });
+
+    it('/v1/business-base/portfolios/:portfolioId (GET) - Should include recoveredValue in response', async () => {
+      const portfolio = createPortfolioFake('Portfolio Single Recovered Value Test', 5, true);
+      const customerFake = await createCustomerFake(
+        'Single Recovered Value User',
+        'single_recovered_value_user',
+        '<EMAIL>',
+        '88.714.997/0001-02',
+        '+5511987555997',
+      );
+
+      // Create portfolio
+      const { body: createBody } = await request(app.getHttpServer())
+        .post('/api/v1/business-base/portfolios/import')
+        .attach('file', `${__dirname}/data/test-portfolio.csv`)
+        .field('name', portfolio.name)
+        .field('workflowId', testWorkflowId)
+        .field('workExpression', portfolio.workExpression)
+        .field('executeImmediately', portfolio.executeImmediately.toString())
+        .field('idleAfter', portfolio.idleAfter.toString())
+        .field('communicationChannel', portfolio.communicationChannel)
+        .set('Authorization', `Bearer ${customerFake.token}`)
+        .expect(201);
+
+      const portfolioId = createBody.data.id;
+
+      // Get single portfolio
+      const { body: getBody } = await request(app.getHttpServer())
+        .get(`/api/v1/business-base/portfolios/${portfolioId}`)
+        .set('Authorization', `Bearer ${customerFake.token}`)
+        .expect(200);
+
+      expect(getBody).toBeDefined();
+      expect(getBody.data).toBeDefined();
+      expect(getBody.data.recoveredValue).toBeDefined();
+      expect(typeof getBody.data.recoveredValue).toBe('string');
+    });
+
+    it('/v1/business-base/portfolios/:portfolioId (GET) - Should filter recoveredValue by date range', async () => {
+      const portfolio = createPortfolioFake('Portfolio Single Date Filter Test', 5, true);
+      const customerFake = await createCustomerFake(
+        'Single Date Filter User',
+        'single_date_filter_user',
+        '<EMAIL>',
+        '88.714.996/0001-02',
+        '+5511987555996',
+      );
+
+      // Create portfolio
+      const { body: createBody } = await request(app.getHttpServer())
+        .post('/api/v1/business-base/portfolios/import')
+        .attach('file', `${__dirname}/data/test-portfolio.csv`)
+        .field('name', portfolio.name)
+        .field('workflowId', testWorkflowId)
+        .field('workExpression', portfolio.workExpression)
+        .field('executeImmediately', portfolio.executeImmediately.toString())
+        .field('idleAfter', portfolio.idleAfter.toString())
+        .field('communicationChannel', portfolio.communicationChannel)
+        .set('Authorization', `Bearer ${customerFake.token}`)
+        .expect(201);
+
+      const portfolioId = createBody.data.id;
+      const startDate = '2024-01-01T00:00:00.000Z';
+      const endDate = '2024-12-31T23:59:59.999Z';
+
+      // Get single portfolio with date filtering
+      const { body: getBody } = await request(app.getHttpServer())
+        .get(`/api/v1/business-base/portfolios/${portfolioId}`)
+        .query({ startDate, endDate })
+        .set('Authorization', `Bearer ${customerFake.token}`)
+        .expect(200);
+
+      expect(getBody).toBeDefined();
+      expect(getBody.data).toBeDefined();
+      expect(getBody.data.recoveredValue).toBeDefined();
+      expect(typeof getBody.data.recoveredValue).toBe('string');
+    });
+  });
 });
diff --git a/test/unit/business-base/portfolio.use-case.spec.ts b/test/unit/business-base/portfolio.use-case.spec.ts
index 4c0e9e39..b0299bdb 100644
--- a/test/unit/business-base/portfolio.use-case.spec.ts
+++ b/test/unit/business-base/portfolio.use-case.spec.ts
@@ -24,6 +24,12 @@ describe('PortfolioUseCase - Tax Rules Calculations', () => {
   const mockPortfolioItemCustomDataAdapter = { create: jest.fn(), get: jest.fn() };
   const mockMiddlewareResponseOutputAdapter = { create: jest.fn(), get: jest.fn() };
   const mockInfraWorkflowAdapter = { create: jest.fn(), get: jest.fn() };
+  const mockCollectCashStatsAdapter = {
+    getTotalRecoveredValueByPortfolioId: jest.fn(),
+    getTotalRecoveredValueByPortfolioIdWithDateRange: jest.fn(),
+    getTotalRecoveredValueByCustomerId: jest.fn(),
+    getTotalRecoveredValueByCustomerIdWithDateRange: jest.fn(),
+  };
 
   beforeEach(async () => {
     const module: TestingModule = await Test.createTestingModule({
@@ -40,6 +46,7 @@ describe('PortfolioUseCase - Tax Rules Calculations', () => {
         { provide: 'PortfolioItemCustomDataPort', useValue: mockPortfolioItemCustomDataAdapter },
         { provide: 'MiddlewareResponseOutputPort', useValue: mockMiddlewareResponseOutputAdapter },
         { provide: 'InfraWorkflowPort', useValue: mockInfraWorkflowAdapter },
+        { provide: 'CollectCashStatsPort', useValue: mockCollectCashStatsAdapter },
       ],
     }).compile();
 
@@ -572,4 +579,110 @@ describe('PortfolioUseCase - Tax Rules Calculations', () => {
       expect(result.VALOR_TOTAL_CORRIGIDO).toMatch(/^\d+\.\d{2}$/);
     });
   });
+
+  describe('Recovered Value Functionality', () => {
+    beforeEach(() => {
+      jest.clearAllMocks();
+    });
+
+    describe('getTotalRecoveredValueByCustomer', () => {
+      it('should return total recovered value without date filtering', async () => {
+        const customerId = 'customer-123';
+        const expectedValue = 150000; // 1500.00 in cents
+
+        mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerId.mockResolvedValue(
+          expectedValue,
+        );
+
+        const result = await useCase.getTotalRecoveredValueByCustomer(customerId);
+
+        expect(result).toBe(expectedValue);
+        expect(mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerId).toHaveBeenCalledWith(
+          customerId,
+        );
+        expect(
+          mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerIdWithDateRange,
+        ).not.toHaveBeenCalled();
+      });
+
+      it('should return total recovered value with date filtering', async () => {
+        const customerId = 'customer-123';
+        const startDate = new Date('2024-01-01');
+        const endDate = new Date('2024-12-31');
+        const expectedValue = 75000; // 750.00 in cents
+
+        mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerIdWithDateRange.mockResolvedValue(
+          expectedValue,
+        );
+
+        const result = await useCase.getTotalRecoveredValueByCustomer(
+          customerId,
+          startDate,
+          endDate,
+        );
+
+        expect(result).toBe(expectedValue);
+        expect(
+          mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerIdWithDateRange,
+        ).toHaveBeenCalledWith(customerId, startDate, endDate);
+        expect(
+          mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerId,
+        ).not.toHaveBeenCalled();
+      });
+
+      it('should return total recovered value with only start date', async () => {
+        const customerId = 'customer-123';
+        const startDate = new Date('2024-01-01');
+        const expectedValue = 100000; // 1000.00 in cents
+
+        mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerIdWithDateRange.mockResolvedValue(
+          expectedValue,
+        );
+
+        const result = await useCase.getTotalRecoveredValueByCustomer(customerId, startDate);
+
+        expect(result).toBe(expectedValue);
+        expect(
+          mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerIdWithDateRange,
+        ).toHaveBeenCalledWith(customerId, startDate, undefined);
+      });
+
+      it('should return total recovered value with only end date', async () => {
+        const customerId = 'customer-123';
+        const endDate = new Date('2024-12-31');
+        const expectedValue = 125000; // 1250.00 in cents
+
+        mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerIdWithDateRange.mockResolvedValue(
+          expectedValue,
+        );
+
+        const result = await useCase.getTotalRecoveredValueByCustomer(
+          customerId,
+          undefined,
+          endDate,
+        );
+
+        expect(result).toBe(expectedValue);
+        expect(
+          mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerIdWithDateRange,
+        ).toHaveBeenCalledWith(customerId, undefined, endDate);
+      });
+
+      it('should handle zero recovered value', async () => {
+        const customerId = 'customer-123';
+        const expectedValue = 0;
+
+        mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerId.mockResolvedValue(
+          expectedValue,
+        );
+
+        const result = await useCase.getTotalRecoveredValueByCustomer(customerId);
+
+        expect(result).toBe(expectedValue);
+        expect(mockCollectCashStatsAdapter.getTotalRecoveredValueByCustomerId).toHaveBeenCalledWith(
+          customerId,
+        );
+      });
+    });
+  });
 });
