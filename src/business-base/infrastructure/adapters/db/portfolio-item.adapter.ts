import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { PrismaService } from '@common/prisma/prisma.service';
import { PortfolioItemEntity } from '@business-base/domain/entities/portfolio-item.entity';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { Injectable } from '@nestjs/common';
import { PortfolioExecutionStatus, PortfolioItemStatus, RecordStatus } from '@common/enums';
import { Prisma } from '@prisma/client';
import { ProxyPrismaModel } from '@common/pagination/prisma-proxy';
import { Paginated } from '@common/pagination/paginated';
import { randomUUID } from 'crypto';

@Injectable()
export class PortfolioItemAdapter
  extends PrismaCommonAdapter<PortfolioItemEntity>
  implements PortfolioItemPort {
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'portfolioItem');
  }

  async fetchAndQueuePendingItems(
    portfolioId: string,
    limit: number,
    queueUrl: string,
    createBatchAndQueueItems: (
      portfolioItemsIds: { id: string }[],
      portfolioId: string,
      queueUrl: string,
      isFirstMessage: boolean,
      isFollowUp: boolean,
    ) => Promise<void>,
  ): Promise<void> {
    return this.prisma.client.$transaction(
      async tx => {
        const items = await tx.$queryRaw<{ id: string }[]>(Prisma.sql`
            SELECT "id"
            FROM "business_base"."portfolio_item"
            WHERE "portfolio_id" = ${portfolioId}::uuid
          AND "current_status" = ${PortfolioItemStatus.PENDING}
              AND "status" = ${RecordStatus.ACTIVE}
                LIMIT ${limit}
                FOR
            UPDATE SKIP LOCKED;
        `);

        if (items && items.length > 0) {
          const ids = items.map(item => item.id);
          await tx.$executeRaw(
            Prisma.sql`
                UPDATE "business_base"."portfolio_item"
                SET "current_status" = ${PortfolioItemStatus.IN_PROGRESS}
                WHERE "id" IN (${Prisma.join(ids.map(id => Prisma.sql`${id}::uuid`))});
            `,
          );

          // Execute history inserts sequentially to avoid connection pool issues
          for (const item of items) {
            await tx.$executeRaw(
              Prisma.sql`
                  INSERT INTO "business_base"."portfolio_item_execution_history"("id", "portfolio_item_id",
                                                                                 "old_status",
                                                                                 "new_status", "created_at",
                                                                                 "updated_at",
                                                                                 "reason",
                                                                                 "status")
                  VALUES (${randomUUID()}::uuid, ${item.id}::uuid, ${PortfolioItemStatus.PENDING},
                          ${PortfolioItemStatus.IN_PROGRESS}, NOW(), NOW(),
                          'Item was fetched and queued to workflow processing',
                          ${RecordStatus.ACTIVE});
              `,
            );
          }

          await createBatchAndQueueItems(items, portfolioId, queueUrl, true, false);
        }
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 30000,
        timeout: 30000,
      },
    );
  }

  async fetchAndQueueFollowUpItems(
    portfolioId: string,
    limit: number,
    queueUrl: string,
    afterMinutes: number,
    maxFollowUps: number,
    createBatchAndQueueItems: (
      portfolioItemsIds: { id: string }[],
      portfolioId: string,
      queueUrl: string,
      isFirstMessage: boolean,
      isFollowUp: boolean,
    ) => Promise<void>,
  ): Promise<void> {
    return this.prisma.client.$transaction(
      async tx => {
        // Calculate the cutoff time in JavaScript instead of using database functions
        const now = new Date();
        const cutoffTime = new Date(now.getTime() - afterMinutes * 60 * 1000);

        const items = await tx.$queryRaw<
          { id: string; followUpCount: number; currentStatus: string }[]
        >(Prisma.sql`
            SELECT id,
                   follow_up_count as "followUpCount",
                   current_status as "currentStatus"
            FROM business_base.portfolio_item
            WHERE portfolio_id = ${portfolioId}::uuid
            AND last_interaction IS NOT NULL
            AND last_message_sent_at <= ${cutoffTime}
	          AND status = ${RecordStatus.ACTIVE}
            AND current_status IN ( ${PortfolioItemStatus.IN_PROGRESS}, ${PortfolioItemStatus.FOLLOWED_UP})
            ORDER BY last_interaction ASC
                LIMIT ${limit}
                FOR
            UPDATE SKIP LOCKED;
        `);

        if (items && items.length > 0) {
          const failedItems = items.filter(item => item.followUpCount >= maxFollowUps);

          for (const item of failedItems) {
            if (item.followUpCount >= maxFollowUps) {
              await tx.$executeRaw(
                Prisma.sql`
                  UPDATE "business_base"."portfolio_item"
                  SET "current_status" = ${PortfolioItemStatus.FINISHED}
                  WHERE "id" = ${item.id}::uuid;
                `,
              );

              await tx.$executeRaw(
                Prisma.sql`
                  INSERT INTO "business_base"."portfolio_item_execution_history"("id", "portfolio_item_id",
                                                                                 "old_status",
                                                                                 "new_status", "created_at",
                                                                                 "updated_at",
                                                                                 "reason",
                                                                                 "status")
                  VALUES (${randomUUID()}::uuid, ${item.id}::uuid, ${item.currentStatus},
                          ${PortfolioItemStatus.FINISHED}, ${now}, ${now},
                          'Item was fetched and set as FINISHED because of max follow-up count.',
                          ${RecordStatus.ACTIVE});
                `,
              );
            }
          }

          const itemsToFollow = items.filter(item => failedItems.includes(item) === false);

          for (const item of itemsToFollow) {
            if (item.currentStatus !== PortfolioItemStatus.FOLLOWED_UP) {
              await tx.$executeRaw(
                Prisma.sql`
                  UPDATE "business_base"."portfolio_item"
                  SET "current_status" = ${PortfolioItemStatus.FOLLOWED_UP}
                  WHERE "id" = ${item.id}::uuid;
                `,
              );

              await tx.$executeRaw(
                Prisma.sql`
                  INSERT INTO "business_base"."portfolio_item_execution_history"("id", "portfolio_item_id",
                                                                                 "old_status",
                                                                                 "new_status", "created_at",
                                                                                 "updated_at",
                                                                                 "reason",
                                                                                 "status")
                  VALUES (${randomUUID()}::uuid, ${item.id}::uuid, ${item.currentStatus},
                          ${PortfolioItemStatus.FOLLOWED_UP}, ${now}, ${now},
                          'Item was fetched and set as FOLLOWED-UP because last interaction was more than ${afterMinutes}
                             minutes ago.',
                          ${RecordStatus.ACTIVE});
                `,
              );
            }

            await tx.$executeRaw(
              Prisma.sql`
                UPDATE "business_base"."portfolio_item"
                SET "follow_up_count"   = ${item.followUpCount + 1},
                    "last_follow_up_at" = ${now}
                WHERE "id" = ${item.id}::uuid;
              `,
            );
          }

          await createBatchAndQueueItems(itemsToFollow, portfolioId, queueUrl, false, true);
        }
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 30000,
        timeout: 30000,
      },
    );
  }

  async fetchAndQueueScheduledFollowUpItems(
    limit: number,
    queueUrl: string,
    createBatchAndQueueItems: (
      portfolioItemsIds: { id: string }[],
      portfolioId: string,
      queueUrl: string,
      isFirstMessage: boolean,
      isFollowUp: boolean,
    ) => Promise<void>,
  ): Promise<void> {
    return this.prisma.client.$transaction(
      async tx => {
        const now = new Date();
        const items = await tx.$queryRaw<
          {
            id: string;
            portfolioitemid: string;
            workflowid: string;
            currentstatus: string;
            portfolioid: string;
          }[]
        >(Prisma.sql`
          SELECT sfupi.id                as id,
                 sfupi.portfolio_item_id as portfolioitemid,
                 sfupi.workflow_id       as workflowid,
                 pi.current_status       as currentstatus,
                 p.id                    as portfolioid
          FROM business_base.portfolio_item_scheduled_follow_up sfupi
                 join business_base.portfolio_item pi on pi.id = sfupi.portfolio_item_id
                 join business_base.portfolio p on p.id = pi.portfolio_id
          where sfupi.status = ${RecordStatus.ACTIVE}
            and pi.current_status != ${PortfolioItemStatus.CANCELLED}
            and pi.current_status != ${PortfolioItemStatus.PAUSED}
            and pi.status = ${RecordStatus.ACTIVE}
            and p.status = ${RecordStatus.ACTIVE}
            and p.execution_status != ${PortfolioExecutionStatus.CANCELLED}
            and p.execution_status != ${PortfolioExecutionStatus.PAUSED}
            and sfupi.is_done is false
            and sfupi.done_at is null
            and sfupi.scheduled_to:: DATE <= ${now}
          ORDER BY sfupi.created_at ASC
            LIMIT ${limit}
            FOR
          UPDATE SKIP LOCKED;
        `);

        if (items && items.length >= 0) {
          const itemsByPortfolioId = new Map<
            string,
            {
              id: string;
              portfolioitemid: string;
              workflowid: string;
              currentstatus: string;
              portfolioid: string;
            }[]
          >();
          for (const item of items) {
            if (!itemsByPortfolioId.has(item.portfolioid)) {
              itemsByPortfolioId.set(item.portfolioid, []);
            }
            itemsByPortfolioId.get(item.portfolioid)!.push(item);
          }

          for (const [portfolioId, groupedItems] of itemsByPortfolioId.entries()) {
            for (const item of groupedItems) {
              if (item.currentstatus !== PortfolioItemStatus.FOLLOWED_UP) {
                await tx.$executeRaw(
                  Prisma.sql`
                    UPDATE "business_base"."portfolio_item"
                    SET "current_status" = ${PortfolioItemStatus.FOLLOWED_UP}
                    WHERE "id" = ${item.id}::uuid;
                  `,
                );

                await tx.$executeRaw(
                  Prisma.sql`
                    INSERT INTO "business_base"."portfolio_item_execution_history"("id", "portfolio_item_id",
                                                                                   "old_status",
                                                                                   "new_status", "created_at",
                                                                                   "updated_at",
                                                                                   "reason",
                                                                                   "status")
                    VALUES (${randomUUID()}::uuid, ${item.id}::uuid, ${item.currentstatus},
                            ${PortfolioItemStatus.FOLLOWED_UP}, ${now}, ${now},
                            'Feito acompanhamento agendado após solicitação do cliente.',
                            ${RecordStatus.ACTIVE});
                  `,
                );
              }

              await tx.$executeRaw(
                Prisma.sql`
                  UPDATE "business_base"."portfolio_item_scheduled_follow_up"
                  SET "is_done" = true,
                      "done_at" = ${now}
                  WHERE "id" = ${item.id}::uuid;
                `,
              );
            }

            await createBatchAndQueueItems(
              groupedItems.map(i => ({ id: i.portfolioitemid })),
              portfolioId,
              queueUrl,
              false,
              true,
            );
          }
        }
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 30000,
        timeout: 30000,
      },
    );
  }

  async countByPortfolioId(portfolioId: string): Promise<number> {
    return this.prisma.client.portfolioItem.count({
      where: {
        portfolioId,
      },
    });
  }

  async countSuccessfulByPortfolioId(portfolioId: string): Promise<number> {
    return this.prisma.client.portfolioItem.count({
      where: {
        currentStatus: PortfolioItemStatus.SUCCEED,
        portfolioId,
      },
    });
  }

  async updateBatchToIdle(date: Date, batchSize: number): Promise<number> {
    const query = Prisma.sql`
      WITH to_update AS (SELECT pi.id
                         FROM business_base.portfolio_item pi
                                JOIN business_base.portfolio p ON pi.portfolio_id = p.id
                         WHERE pi.current_status = ${PortfolioItemStatus.IN_PROGRESS}
                           AND (pi.last_interaction + (p.idle_after * INTERVAL '1 day')) <= ${date}
        LIMIT ${batchSize}
        )
      UPDATE business_base.portfolio_item
      SET current_status = ${PortfolioItemStatus.IDLE}
      WHERE id IN (SELECT id FROM to_update);
    `;
    return this.prisma.client.$executeRaw(query);
  }

  async findManyPaginated(data: any, pagination: any): Promise<Paginated<PortfolioItemEntity>> {
    const portfolioItemModel = ProxyPrismaModel(this.prisma.client.portfolioItem);
    const result = await portfolioItemModel.findManyPaginated(data, pagination);

    return {
      ...result,
      data: result.data.map(item => ({
        ...item,
        currentStatus: item.currentStatus as PortfolioItemStatus,
        line: Number(item.line),
        followUpCount: Number(item.followUpCount),
      })),
    };
  }

  async findAllGroupedByStatus(portfolioId: string): Promise<Record<PortfolioItemStatus, number>> {
    const query = Prisma.sql`SELECT current_status, COUNT(*) AS total
                             FROM business_base.portfolio_item pi2
                             WHERE portfolio_id = ${portfolioId}::uuid
                             GROUP BY current_status
                             ORDER BY current_status;`;
    const result = await this.prisma.client.$queryRaw<
      { current_status: PortfolioItemStatus; total: number }[]
    >(query);

    // Transform the result into a Record<PortfolioItemStatus, number>
    return result.reduce(
      (acc, { current_status, total }) => {
        acc[current_status] = total;
        return acc;
      },
      {} as Record<PortfolioItemStatus, number>,
    );
  }

  async findActiveByPortfolioIdsAndPhoneNumber(
    portfolioIds: string[],
    phoneNumber: string,
  ): Promise<PortfolioItemEntity[]> {
    const items = await this.prisma.client.portfolioItem.findMany({
      where: {
        phoneNumber,
        currentStatus: {
          in: [
            PortfolioItemStatus.IN_PROGRESS,
            PortfolioItemStatus.PENDING,
            PortfolioItemStatus.PAUSED,
            PortfolioItemStatus.UNLINKED,
          ],
        },
        portfolioId: { in: portfolioIds },
        status: RecordStatus.ACTIVE,
      },
    });

    return items.map(item => ({
      ...item,
      currentStatus: item.currentStatus as PortfolioItemStatus,
      line: Number(item.line),
      followUpCount: Number(item.followUpCount),
    }));
  }

  async findActiveByPortfolioIdAndLine(
    portfolioId: string,
    line: number,
  ): Promise<PortfolioItemEntity> {
    const item = await this.prisma.client.portfolioItem.findFirst({
      where: {
        line,
        portfolioId: portfolioId,
        status: RecordStatus.ACTIVE,
      },
    });

    if (!item) {
      return null;
    }

    return {
      ...item,
      currentStatus: (item?.currentStatus as PortfolioItemStatus) || null,
      line: Number(item.line),
      followUpCount: Number(item.followUpCount),
    };
  }

  async findToAnswerByPortfolioIdAndPhoneNumber(
    portfolioIds: string[],
    phoneNumber: string,
  ): Promise<PortfolioItemEntity[]> {
    const items = await this.prisma.client.portfolioItem.findMany({
      where: {
        phoneNumber,
        currentStatus: {
          in: [
            PortfolioItemStatus.PENDING,
            PortfolioItemStatus.IN_PROGRESS,
            PortfolioItemStatus.SUCCEED,
            PortfolioItemStatus.FAILED,
            PortfolioItemStatus.IDLE,
            PortfolioItemStatus.UNLINKED,
            PortfolioItemStatus.FOLLOWED_UP,
            PortfolioItemStatus.OPTED_OUT,
            PortfolioItemStatus.FINISHED,
          ],
        },
        portfolioId: { in: portfolioIds },
        status: RecordStatus.ACTIVE,
      },
    });

    return items.map(item => ({
      ...item,
      currentStatus: item.currentStatus as PortfolioItemStatus,
      line: Number(item.line),
    }));
  }

  async findPendingByPortfolioIdAndPhoneNumber(
    portfolioIds: string[],
    phoneNumber: string,
  ): Promise<PortfolioItemEntity[]> {
    const items = await this.prisma.client.portfolioItem.findMany({
      where: {
        phoneNumber,
        currentStatus: {
          in: [PortfolioItemStatus.PENDING],
        },
        portfolioId: { in: portfolioIds },
        status: RecordStatus.ACTIVE,
      },
    });

    return items.map(item => ({
      ...item,
      currentStatus: item.currentStatus as PortfolioItemStatus,
      line: Number(item.line),
      followUpCount: Number(item.followUpCount),
    }));
  }

  async findPortfolioItemsWithAiOnlyInteractionCountByDate(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<number> {
    const query = Prisma.sql`SELECT COUNT(item.id) as total
                             FROM business_base.portfolio portfolio
                                    JOIN business_base.portfolio_item item ON (item.portfolio_id = portfolio.id)
                                    LEFT JOIN business_base.portfolio_item_execution_history history_unlinked
                                              ON (history_unlinked.portfolio_item_id = item.id AND
                                                  history_unlinked.new_status = 'UNLINKED')
                             WHERE 1 = 1
                               AND portfolio.customer_id = ${customerId}::uuid
                               AND item.current_status = ${PortfolioItemStatus.SUCCEED}
                               AND history_unlinked.id IS NULL
                               AND item.created_at:: DATE BETWEEN ${dateStart}
                               AND ${dateEnd}`;
    const result = await this.prisma.client.$queryRaw<{ total: number }[]>(query);

    return result.length > 0 ? result[0].total : 0;
  }
}
