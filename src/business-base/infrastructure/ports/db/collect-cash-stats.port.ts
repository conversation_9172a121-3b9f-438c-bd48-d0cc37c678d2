import { DbCommonPort } from '@common/db/ports/common.port';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';

export interface CollectCashStatsPort extends DbCommonPort<CollectCashStatsEntity> {
  getTotalRecoveredValueByCustomerId(customerId: string): Promise<number>;
  getTotalRecoveredValueByPortfolioId(portfolioId: string): Promise<number>;
  getTotalRecoveredValueByCustomerIdWithDateRange(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number>;
  getTotalRecoveredValueByPortfolioIdWithDateRange(
    portfolioId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number>;
}
