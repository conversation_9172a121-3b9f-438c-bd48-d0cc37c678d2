import { IsBoolean, IsDate, IsEnum, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { PortfolioExecutionStatus, PortfolioImportStatus } from '@common/enums';
import { PortfolioDto } from '@business-base/application/dto/in/portfolio.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ResponsePortfolioDto extends PortfolioDto {
  @IsUUID('4')
  readonly customerId: string;

  @IsString()
  readonly workExpression: string;

  @IsString()
  readonly followUpExpression: string;

  @IsUUID('4')
  readonly followUpWorkflowId: string;

  @IsNumber()
  readonly followUpAfter: number = 24;

  @IsNumber()
  readonly maxFollowUps: number = 5;

  @IsEnum(PortfolioExecutionStatus)
  readonly executionStatus: PortfolioExecutionStatus;

  @IsEnum(PortfolioImportStatus)
  readonly importStatus: PortfolioImportStatus;

  @IsBoolean()
  readonly executeImmediately: boolean = false;

  @IsString()
  readonly originalFileName: string;

  @IsNumber()
  readonly totalQuantity: number = 0;

  @IsNumber()
  readonly processedQuantity: number = 0;

  @IsNumber()
  readonly totalSuccessQuantity: number = 0;

  @IsNumber()
  readonly totalFailedQuantity: number = 0;

  @IsDate()
  readonly importFinishedAt?: Date;

  @IsDate()
  readonly createdAt: Date;

  @IsString()
  readonly status: string;

  @IsString()
  readonly fileUrl: string;

  @IsString()
  readonly timezoneUTC: string = '-3';

  @ApiProperty({
    description: 'Total recovered value formatted as BRL currency',
    example: 'R$ 1.500,00',
    required: false,
  })
  @IsString()
  @IsOptional()
  readonly recoveredValue?: string;
}
